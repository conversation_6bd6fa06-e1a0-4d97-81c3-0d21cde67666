#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将黑神话悟空妖怪平生录markdown文件转换为JSON格式
分析文件结构：
1. 一级标题：# 一、小妖  # 二、头目  # 三、妖王  # 四、人物
2. 二级标题：## 1.1 狼斥候 (ID + 名称)
3. 诗词：> 开头的引用块
4. 故事：普通段落文本
5. 图片：![alt](path) 格式
"""

import re
import json
import os

def escape_json_string(text):
    """转义JSON字符串中的特殊字符"""
    if not text:
        return ""
    
    # 替换需要转义的字符
    text = text.replace('\\', '\\\\')  # 反斜杠
    text = text.replace('"', '\\"')    # 双引号
    text = text.replace('\n', '\\n')   # 换行符
    text = text.replace('\r', '\\r')   # 回车符
    text = text.replace('\t', '\\t')   # 制表符
    text = text.replace('\b', '\\b')   # 退格符
    text = text.replace('\f', '\\f')   # 换页符
    
    return text

def get_monster_type(name):
    """根据名字推断妖怪类型"""
    type_mapping = {
        '狼': '狼妖', '鼠': '鼠妖', '蛇': '蛇妖', '石': '石精', '骨': '骨妖',
        '鸦': '鸦妖', '蝠': '蝠妖', '龙': '龙族', '虎': '虎妖', '猴': '猴妖',
        '猿': '猴妖', '蛙': '蛙妖', '呱': '蛙妖', '蜘蛛': '蛛妖', '蛛': '蛛妖',
        '虫': '虫妖', '螂': '虫妖', '鱼': '鱼妖', '僧': '僧妖', '菌': '菌精',
        '参': '参精', '人参': '参精', '鼬': '鼬妖', '匪': '人类', '尸': '尸妖',
        '蝜': '虫妖', '蝂': '虫妖', '敌': '蝎妖', '蝎': '蝎妖', '夜叉': '夜叉',
        '魔': '魔族', '佛': '佛族', '仙': '仙人', '神': '神仙', '土地': '土地神',
        '灵官': '神仙', '太子': '神仙', '弥勒': '佛祖', '真君': '神仙',
        '八戒': '猪妖', '牛': '牛妖', '马': '马妖', '狐': '狐妖', '鹤': '鹤仙',
        '员外': '人类', '道人': '道士', '秀士': '书生'
    }
    
    for key, value in type_mapping.items():
        if key in name:
            return value
    return '其他'

def get_monster_tags(name, story):
    """根据名字和故事生成标签"""
    tags = []
    
    # 根据名字添加标签
    name_tags = {
        '斥候': '斥候', '剑客': '剑客', '校卫': '校卫', '弓手': '弓手',
        '侍卫': '侍卫', '刺客': '刺客', '力士': '力士', '护院': '护院',
        '都尉': '都尉', '司空': '司空', '禁卫': '禁卫', '先锋': '先锋',
        '大王': '王者', '大圣': '圣者', '魔君': '魔君', '菩萨': '菩萨',
        '真君': '真君', '太子': '太子', '土地': '土地', '灵官': '灵官'
    }
    
    for key, tag in name_tags.items():
        if key in name and tag not in tags:
            tags.append(tag)
    
    # 根据故事内容添加标签
    story_tags = {
        '修炼': '修炼', '求道': '求道', '升迁': '升迁', '忠诚': '忠诚',
        '忠心': '忠诚', '勤劳': '勤劳', '机智': '机智', '聪明': '机智',
        '贪心': '贪心', '贪财': '贪心', '复仇': '复仇', '报仇': '复仇',
        '善良': '善良', '善意': '善良', '悲剧': '悲剧', '悲惨': '悲剧',
        '嗜酒': '嗜酒', '梦境': '梦境', '善射': '善射', '残暴': '残暴',
        '狡诈': '狡诈', '孤僻': '孤僻', '神风': '神通', '法力': '法力'
    }
    
    for key, tag in story_tags.items():
        if key in story and tag not in tags:
            tags.append(tag)
    
    return tags[:5]  # 限制标签数量

def parse_markdown_to_json(md_file_path, json_file_path):
    """解析markdown文件并生成JSON数据"""
    
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 基础数据结构
    data = {
        "title": "黑神话悟空妖怪平生录",
        "description": "收录了黑神话悟空游戏中各种妖怪的生平故事",
        "categories": [
            {
                "id": "1",
                "name": "小妖",
                "description": "各种小妖怪的故事",
                "color": "#4CAF50"
            },
            {
                "id": "2", 
                "name": "头目",
                "description": "妖怪头目的故事",
                "color": "#FF9800"
            },
            {
                "id": "3",
                "name": "妖王",
                "description": "妖王级别的故事",
                "color": "#F44336"
            },
            {
                "id": "4",
                "name": "人物",
                "description": "重要人物的故事",
                "color": "#9C27B0"
            }
        ],
        "monsters": []
    }
    
    # 分割章节
    sections = re.split(r'^# ([一二三四])、(.+)$', content, flags=re.MULTILINE)
    
    current_category = None
    current_category_id = None
    
    for i in range(1, len(sections), 3):
        if i + 2 < len(sections):
            chapter_num = sections[i]
            chapter_name = sections[i + 1]
            chapter_content = sections[i + 2]
            
            # 确定分类
            category_mapping = {
                "一": ("小妖", "1"),
                "二": ("头目", "2"), 
                "三": ("妖王", "3"),
                "四": ("人物", "4")
            }
            
            if chapter_num in category_mapping:
                current_category, current_category_id = category_mapping[chapter_num]
            
            # 解析每个妖怪/人物
            # 先按## 分割每个条目
            entries = re.split(r'\n## ', chapter_content)

            monsters = []
            for entry in entries:
                if not entry.strip():
                    continue

                # 如果不是以数字开头，跳过
                if not re.match(r'^\d+\.\d+', entry):
                    continue

                # 解析ID和名称
                lines = entry.split('\n')
                if not lines:
                    continue

                first_line = lines[0].strip()
                match = re.match(r'^([\d.]+)\s+(.+)$', first_line)
                if not match:
                    continue

                monster_id = match.group(1)
                monster_name = match.group(2)

                # 提取剩余内容
                remaining_content = '\n'.join(lines[1:])

                monsters.append((monster_id, monster_name, remaining_content))
            
            for monster_match in monsters:
                monster_id = monster_match[0].strip()
                monster_name = monster_match[1].strip()
                content = monster_match[2].strip()

                # 分离诗词和故事
                # 诗词通常在开头，以 > 开始，可能有多行
                poem_lines = []
                story_lines = []
                lines = content.split('\n')

                # 标记是否还在诗词部分
                in_poem = True

                for line in lines:
                    if line.strip().startswith('> '):
                        # 这是诗词行
                        poem_lines.append(line.strip()[2:])  # 去掉 "> "
                    elif line.strip() == '' and in_poem:
                        # 空行，可能是诗词和故事的分隔
                        continue
                    else:
                        # 这是故事内容
                        in_poem = False
                        if line.strip():  # 忽略空行
                            story_lines.append(line)

                poem = '\\n'.join(poem_lines) if poem_lines else ""
                story_content = '\n'.join(story_lines)

                # 分离故事和图片
                image_match = re.search(r'!\[([^\]]*)\]\(([^)]+)\)', story_content)
                image = image_match.group(2) if image_match else ""

                # 提取故事（去掉图片部分）
                story = re.sub(r'!\[([^\]]*)\]\(([^)]+)\)', '', story_content).strip()
                
                # 根据名字推断类型和标签
                monster_type = get_monster_type(monster_name)
                tags = get_monster_tags(monster_name, story)
                
                # 转义JSON字符串
                monster_data = {
                    "id": escape_json_string(monster_id),
                    "name": escape_json_string(monster_name),
                    "category": escape_json_string(current_category),
                    "categoryId": escape_json_string(current_category_id),
                    "type": escape_json_string(monster_type),
                    "poem": escape_json_string(poem),
                    "story": escape_json_string(story),
                    "image": escape_json_string(image),
                    "tags": [escape_json_string(tag) for tag in tags]
                }
                
                data["monsters"].append(monster_data)
    
    # 保存JSON文件
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"成功解析 {len(data['monsters'])} 个妖怪/人物数据")
    print(f"数据已保存到 {json_file_path}")
    return data

if __name__ == "__main__":
    # 检查文件是否存在
    md_file = "黑神话悟空妖怪平生录.md"
    json_file = "data.json"
    
    if not os.path.exists(md_file):
        print(f"错误：找不到文件 {md_file}")
        exit(1)
    
    try:
        result = parse_markdown_to_json(md_file, json_file)
        print("转换完成！")
        
        # 显示统计信息
        categories_count = {}
        for monster in result["monsters"]:
            category = monster["category"]
            categories_count[category] = categories_count.get(category, 0) + 1
        
        print("\n各分类统计：")
        for category, count in categories_count.items():
            print(f"  {category}: {count} 个")
            
    except Exception as e:
        print(f"转换过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
