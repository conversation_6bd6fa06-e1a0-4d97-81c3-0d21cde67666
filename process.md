# 黑神话悟空妖怪平生录 - Markdown转JSON数据处理过程

## 项目概述

本项目将《黑神话悟空妖怪平生录》markdown文件转换为结构化的JSON数据格式，为后续的Vue前端展示做准备。

## 文件结构分析

### 原始Markdown文件结构

文件 `黑神话悟空妖怪平生录.md` 的结构如下：

1. **一级标题**：章节分类
   - `# 一、小妖`
   - `# 二、头目`
   - `# 三、妖王`
   - `# 四、人物`

2. **二级标题**：妖怪条目
   - 格式：`## ID 名称`（如：`## 1.1 狼斥候`）

3. **诗词部分**：
   - 以 `> ` 开头的引用块
   - 通常为4行诗句

4. **故事内容**：
   - 普通段落文本
   - 包含妖怪的详细生平故事

5. **图片**：
   - 格式：`![alt](path)`
   - 位于故事内容末尾

## 数据转换过程

### 1. 创建转换脚本

创建了 `convert_md_to_json.py` 脚本，主要功能包括：

- **文件解析**：使用正则表达式解析markdown结构
- **数据提取**：分离诗词、故事和图片内容
- **字符转义**：处理JSON特殊字符的转义
- **分类推断**：根据名称自动推断妖怪类型
- **标签生成**：基于名称和故事内容生成相关标签

### 2. 关键技术点

#### 字符转义处理
```python
def escape_json_string(text):
    """转义JSON字符串中的特殊字符"""
    text = text.replace('\\', '\\\\')  # 反斜杠
    text = text.replace('"', '\\"')    # 双引号
    text = text.replace('\n', '\\n')   # 换行符
    text = text.replace('\r', '\\r')   # 回车符
    text = text.replace('\t', '\\t')   # 制表符
    text = text.replace('\b', '\\b')   # 退格符
    text = text.replace('\f', '\\f')   # 换页符
```

#### 内容分离算法
- 按章节分割文档
- 按 `## ` 分割每个妖怪条目
- 逐行解析诗词和故事内容
- 使用正则表达式提取图片路径

#### 智能分类系统
- 根据名称关键词自动推断妖怪类型（狼妖、鼠妖、石精等）
- 基于名称和故事内容生成相关标签
- 支持35种不同的妖怪类型分类

### 3. 输出JSON结构

生成的 `data.json` 文件结构：

```json
{
  "title": "黑神话悟空妖怪平生录",
  "description": "收录了黑神话悟空游戏中各种妖怪的生平故事",
  "categories": [
    {
      "id": "1",
      "name": "小妖",
      "description": "各种小妖怪的故事",
      "color": "#4CAF50"
    }
    // ... 其他分类
  ],
  "monsters": [
    {
      "id": "1.1",
      "name": "狼斥候",
      "category": "小妖",
      "categoryId": "1",
      "type": "狼妖",
      "poem": "尔小狼，肝胆忠。求道切，赤炯炯。",
      "story": "狼群中有只小妖...",
      "image": "黑神话悟空妖怪平生录/image-20240825195638090.png",
      "tags": ["斥候", "修炼"]
    }
    // ... 其他妖怪数据
  ]
}
```

## 转换结果统计

- **总计妖怪数量**：203个
- **分类统计**：
  - 小妖：90个
  - 头目：55个
  - 妖王：26个
  - 人物：32个

## 数据质量保证

1. **JSON格式验证**：确保生成的JSON文件语法正确
2. **字符转义**：正确处理所有特殊字符
3. **数据完整性**：保留所有原始信息
4. **结构一致性**：所有条目都有统一的数据结构

## 技术特点

1. **自动化处理**：一键转换整个文档
2. **智能分类**：自动推断妖怪类型和生成标签
3. **错误处理**：包含完善的异常处理机制
4. **可扩展性**：易于添加新的分类规则和标签

## 使用方法

```bash
# 运行转换脚本
python convert_md_to_json.py

# 验证JSON格式
python -c "import json; json.load(open('data.json', 'r', encoding='utf-8'))"
```

## 文件清单

- `黑神话悟空妖怪平生录.md` - 原始markdown文件
- `convert_md_to_json.py` - 转换脚本
- `data.json` - 生成的JSON数据文件
- `process.md` - 本开发过程文档

## 后续计划

1. 使用Vue.js创建前端展示界面
2. 实现搜索和筛选功能
3. 添加响应式设计
4. 优化用户体验

---

*转换完成时间：2024年*
*数据版本：v1.0*
